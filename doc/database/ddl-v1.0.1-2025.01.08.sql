ALTER TABLE rule_validate_log ADD execute_time BIGINT NULL COMMENT '执行时间（分钟）';

CREATE TABLE `project_member` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
                                  `user_code` varchar(64) NOT NULL COMMENT '用户编码',
                                  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名称',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET=utf8mb4 COMMENT='项目成员表';

-- 字典表（Dictionary Table）
CREATE TABLE data_quality_dictionary (
                                         id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
                                         code VARCHAR(64) NOT NULL COMMENT '字典编码，唯一标识',
                                         name VARCHAR(128) NOT NULL COMMENT '字典名称',
                                         description VARCHAR(255) DEFAULT NULL COMMENT '字典描述',
                                         status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（1: 启用, 0: 停用）',
                                         created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         UNIQUE (code)
) COMMENT '字典表';

-- 字典项表（Dictionary Item Table）
CREATE TABLE data_quality_dictionary_item (
                                              id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
                                              dictionary_id BIGINT NOT NULL COMMENT '所属字典ID，外键关联字典表',
                                              code VARCHAR(64) NOT NULL COMMENT '字典项编码，唯一标识',
                                              name VARCHAR(128) NOT NULL COMMENT '字典项名称',
                                              value VARCHAR(255) NOT NULL COMMENT '字典项值',
                                              description VARCHAR(255) DEFAULT NULL COMMENT '字典项描述',
                                              order_index INT DEFAULT 0 COMMENT '排序字段，值越小越靠前',
                                              status TINYINT NOT NULL DEFAULT 1 COMMENT '状态（1: 启用, 0: 停用）',
                                              created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              UNIQUE (dictionary_id, code)
) COMMENT '字典项表';


ALTER TABLE data_source MODIFY COLUMN config varchar(500) NULL COMMENT '其他配置信息';


ALTER TABLE data_quality_project MODIFY COLUMN create_by varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人';
ALTER TABLE data_quality_project MODIFY COLUMN update_by varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改人';
ALTER TABLE data_quality_project ADD update_by_name varchar(50) NOT NULL COMMENT '更新人名称';
ALTER TABLE data_quality_project ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';

ALTER TABLE data_quality_project ADD owner_name varchar(50) NOT NULL COMMENT '项目归属人名称';


CREATE TABLE data_set_groups(
                               `id` BIGINT PRIMARY KEY auto_increment COMMENT '主键',
                               `parent_id` BIGINT NOT NULL COMMENT '父分组ID',
                               `name` VARCHAR(255) NOT null COMMENT '分组名称',
                               `description` VARCHAR(500) null COMMENT '分组描述（可选）',
                               `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                               `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                               `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                               `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                               `del_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标识'
);
ALTER TABLE data_set ADD group_id BIGINT NOT NULL COMMENT '分组ID';

ALTER TABLE data_set ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';
ALTER TABLE data_set ADD update_by_name varchar(50) NOT NULL COMMENT '修改人名称';


ALTER TABLE data_set_groups ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';
ALTER TABLE data_set_groups ADD update_by_name varchar(50) NOT NULL COMMENT '修改人名称';

ALTER TABLE data_set MODIFY COLUMN sql_text text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '数据集查询SQL';


ALTER TABLE data_quality_ruleset MODIFY COLUMN project_id bigint(20) NOT NULL COMMENT '项目ID';
ALTER TABLE data_quality_ruleset MODIFY COLUMN enabled tinyint(1) DEFAULT 0 NOT NULL COMMENT '是否启用';
ALTER TABLE data_quality_ruleset MODIFY COLUMN `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'VIEW' NOT NULL COMMENT '规则集类型（例如：TABLE、VIEW）';
ALTER TABLE data_quality_ruleset ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';
ALTER TABLE data_quality_ruleset ADD update_by_name varchar(50) NOT NULL COMMENT '修改人名称';
ALTER TABLE data_quality_ruleset ADD last_status TINYINT DEFAULT 0 NOT NULL COMMENT '最新一次执行状态';


CREATE TABLE `ruleset_quality_person` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `ruleset_id` bigint(20) NOT NULL COMMENT '规则集ID',
                                          `user_code` varchar(64) NOT NULL COMMENT '用户编码',
                                          `user_name` varchar(100) DEFAULT NULL COMMENT '用户名称',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10010 DEFAULT CHARSET=utf8mb4 COMMENT='规则集质量负责人表';


ALTER TABLE data_quality_ruleset ADD quality_score TINYINT NULL COMMENT '质量分（1-10）';

ALTER TABLE ruleset_quality_person MODIFY COLUMN user_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称';
ALTER TABLE ruleset_quality_person MODIFY COLUMN user_name varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称';
ALTER TABLE ruleset_quality_person MODIFY COLUMN user_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户编码';



ALTER TABLE ruleset_quality_person CHANGE user_code user_id varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户唯一标识';
ALTER TABLE data_quality_rule MODIFY COLUMN rule_strength varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'WEAK' NULL COMMENT '规则强度';
DROP TABLE data_quality_template;

-- todo 修改数据
ALTER TABLE data_quality_rule_template MODIFY COLUMN `catalog` tinyint(2) NOT NULL COMMENT '模版分类';

ALTER TABLE data_quality_rule ADD watch_type varchar(10) DEFAULT 'VIEW' NOT NULL COMMENT '监控类型（VIEW、TABLE）';
ALTER TABLE data_quality_rule ADD remark varchar(500) NULL COMMENT '备注';

ALTER TABLE data_quality_rule ADD exception_archive TINYINT DEFAULT 0 NOT NULL COMMENT '异常归档';
ALTER TABLE data_quality_rule ADD archive_mode tinyint(1) NULL COMMENT '归档模式 1-仅归档异常字段 2-归档完整记录';
ALTER TABLE data_quality_rule ADD score_type tinyint(1) DEFAULT 1 NOT NULL COMMENT '记分方式 1-质量校验状态 2-数据合格比例';
ALTER TABLE data_quality_rule ADD quality_score tinyint(2) NULL COMMENT '质量分数(1-10)';
ALTER TABLE data_quality_rule MODIFY COLUMN exception_archive tinyint(1) DEFAULT 0 NOT NULL COMMENT '异常归档';



ALTER TABLE data_quality_rule MODIFY COLUMN create_by varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人';
ALTER TABLE data_quality_rule ADD create_by_name varchar(100) NOT NULL COMMENT '创建人名称';
ALTER TABLE data_quality_rule ADD update_by_name varchar(100) NOT NULL COMMENT '修改人名称';


ALTER TABLE rule_schedule_info ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';
ALTER TABLE rule_schedule_info ADD update_by_name varchar(50) NOT NULL COMMENT '修改人名称';


CREATE TABLE ruleset_validate_log (
                                        id bigint(20) AUTO_INCREMENT PRIMARY KEY COMMENT '唯一标识',
                                        rule_set_id bigint(20) NOT NULL COMMENT '规则集ID',
                                        rule_set_name VARCHAR(255) NOT NULL COMMENT '规则集名称',
                                        rule_exception_count INT DEFAULT 0 COMMENT '规则异常数',
                                        execution_status ENUM('pending', 'running', 'success', 'failed') DEFAULT 'pending' COMMENT '执行状态',
                                        schedule_id bigint(20) COMMENT '调度ID',
                                        schedule_name VARCHAR(255) COMMENT '调度名称',
                                        quality_owner VARCHAR(255) COMMENT '质量负责人',
                                        start_time DATETIME COMMENT '开始时间',
                                        end_time DATETIME COMMENT '结束时间',
                                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        batch_number VARCHAR(64) COMMENT '批次号'
) COMMENT='规则集校验结果日志表';

ALTER TABLE rule_validate_log MODIFY COLUMN column_name varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '被校验的字段名称';
ALTER TABLE rule_validate_log CHANGE validate_time start_time datetime NOT NULL COMMENT '开始时间';
ALTER TABLE rule_validate_log MODIFY COLUMN start_time datetime NOT NULL COMMENT '开始时间';

ALTER TABLE rule_validate_log CHANGE validate_date end_time DATETIME NOT NULL COMMENT '结束时间';
ALTER TABLE rule_validate_log MODIFY COLUMN end_time DATETIME NOT NULL COMMENT '结束时间';

ALTER TABLE rule_validate_log ADD ruleset_name varchar(100) NOT NULL COMMENT '规则集名称';
ALTER TABLE rule_validate_log CHANGE ruleset_name ruleset_name varchar(100) NOT NULL COMMENT '规则集名称' AFTER ruleset_id;
ALTER TABLE rule_validate_log ADD validate_range varchar(100) NOT NULL COMMENT '校验范围';
ALTER TABLE rule_validate_log ADD rule_type TINYINT NOT NULL COMMENT '规则类型';
ALTER TABLE rule_validate_log ADD rule_type_name varchar(100) NULL COMMENT '规则类型名称';
ALTER TABLE rule_validate_log ADD rule_template bigint(20) NOT NULL COMMENT '规则模版ID';
ALTER TABLE rule_validate_log ADD rule_template_name varchar(100) NOT NULL COMMENT '规则模版名称';

ALTER TABLE ruleset_validate_log MODIFY COLUMN start_time datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '开始时间';
ALTER TABLE ruleset_validate_log MODIFY COLUMN end_time datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '结束时间';
ALTER TABLE ruleset_validate_log MODIFY COLUMN schedule_id bigint(20) NOT NULL COMMENT '调度ID';
ALTER TABLE ruleset_validate_log MODIFY COLUMN schedule_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称';
ALTER TABLE ruleset_validate_log MODIFY COLUMN batch_number varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次号';
ALTER TABLE ruleset_validate_log MODIFY COLUMN created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间';
ALTER TABLE ruleset_validate_log MODIFY COLUMN quality_owner varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '质量负责人';


ALTER TABLE ruleset_validate_log CHANGE rule_set_id ruleset_id bigint(20) NOT NULL COMMENT '规则集ID';
ALTER TABLE ruleset_validate_log CHANGE rule_set_name ruleset_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则集名称';
ALTER TABLE ruleset_validate_log MODIFY COLUMN end_time datetime NULL COMMENT '结束时间';
ALTER TABLE rule_validate_log ADD schedule_name varchar(100) NULL COMMENT '调度名称';
ALTER TABLE rule_validate_log CHANGE schedule_name schedule_name varchar(100) NULL COMMENT '调度名称' AFTER schedule_id;
ALTER TABLE rule_validate_log MODIFY COLUMN execute_time bigint(20) NULL COMMENT '执行时间（秒）';

ALTER TABLE rule_validate_log MODIFY COLUMN validate_range varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '校验范围';
ALTER TABLE rule_validate_log CHANGE rule_template rule_template_id bigint(20) NOT NULL COMMENT '规则模版ID';

ALTER TABLE data_source MODIFY COLUMN `catalog` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'catalog';
ALTER TABLE ruleset_validate_log CHANGE created_at create_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间';
ALTER TABLE rule_validate_log ADD batch_number varchar(64) NOT NULL COMMENT '批次号';
