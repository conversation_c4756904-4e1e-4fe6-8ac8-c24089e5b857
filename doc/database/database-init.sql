CREATE TABLE `data_source`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `name` VARCHAR(255) NOT NULL DEFAULT '255' COMMENT '数据源名称',
    `type` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '数据源类型，例如 MySQL、PostgreSQL、Hive 等',
    `catalog` VARCHAR(255) NOT NULL COMMENT 'catalog',
    `url` VARCHAR(255) NOT NULL COMMENT 'url连接',
    `username` VARCHAR(255) NOT NULL DEFAULT '100' COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL DEFAULT '100' COMMENT '密码',
    `config` JSON NOT NULL COMMENT '其他配置信息',
    `remark` VARCHAR(255) NOT NULL COMMENT '备注',
    `create_by` VARCHAR(255) NOT NULL COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL COMMENT '更新人',
    `create_at` DATETIME NOT NULL COMMENT '创建时间',
    `update_at` DATETIME NOT NULL COMMENT '更新时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标识'
) engine=innodb default charset=utf8 comment '数据源信息表';
CREATE TABLE `data_set`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL COMMENT '数据集名称',
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `data_source_id` BIGINT NOT NULL COMMENT '数据源ID',
    `sql` TEXT NOT NULL COMMENT '数据集查询SQL',
    `description` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '数据集描述',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '更新人',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
) engine=innodb default charset=utf8 comment '数据集信息表';
CREATE TABLE `quality_project`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL COMMENT '项目名称',
    `description` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '项目描述',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '修改人',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '修改时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
) engine=innodb default charset=utf8 comment '项目信息表';

CREATE TABLE `data_source_meta`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `data_source_id` BIGINT NOT NULL COMMENT '数据源ID',
    `table_name` VARCHAR(255) NOT NULL COMMENT '表名称',
    `type` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '元数据类型，例如TABLE',
    `nsp_name` VARCHAR(255) NOT NULL DEFAULT '200' COMMENT '命名空间或者数据库名称',
    `remark` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '备注',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '更新人',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '修改时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
);
CREATE TABLE `data_source_meta_column`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `data_source_meta_id` BIGINT NOT NULL COMMENT '数据源元信息表ID',
    `key` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '唯一标识（不变）',
    `pk` TINYINT NOT NULL COMMENT '是否外键',
    `idx` TINYINT NOT NULL COMMENT '是否索引',
    `name` VARCHAR(255) NOT NULL COMMENT '名称',
    `size` INT NOT NULL COMMENT '长度',
    `type` VARCHAR(255) NOT NULL COMMENT '类型',
    `digit` TINYINT NOT NULL COMMENT '精度',
    `remark` VARCHAR(255) NOT NULL COMMENT '备注',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
);
CREATE TABLE `data_set_meta`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `data_set_id` BIGINT NOT NULL COMMENT '数据集ID',
    `key` VARCHAR(255) NOT NULL DEFAULT '64' COMMENT '列ID',
    `name` VARCHAR(255) NOT NULL COMMENT '列名称',
    `alias` VARCHAR(255) NOT NULL COMMENT '列别名',
    `type` TINYINT NOT NULL COMMENT '列类型，数据类型1-字符串 2-数字 3-日期',
    `table_name` VARCHAR(255) NOT NULL COMMENT '表名称',
    `table_alias` VARCHAR(255) NOT NULL COMMENT '表别名',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '更新人',
    `create_time` DATETIME NOT NULL,
    `update_time` DATETIME NOT NULL,
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
);
CREATE TABLE `quality_project`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL COMMENT '项目名称',
    `description` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '项目描述',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '修改人',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '修改时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
);
CREATE TABLE `quality_task`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL COMMENT '任务名称',
    `project_id` BIGINT NOT NULL COMMENT '项目ID',
    `monitor_cron` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '监控任务调度计划',
    `compensate_cron` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '补偿任务调度计划',
    `compensate_number` TINYINT NOT NULL COMMENT '补偿次数',
    `mantis_id` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '运维Mantis ID',
    `emails` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '消息通知邮箱地址，逗号分隔',
    `phone` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '消息通知手机号',
    `notice_template_id` BIGINT NOT NULL COMMENT '通知模版ID',
    `remark` VARCHAR(255) NOT NULL COMMENT '描述',
    `create_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '创建人',
    `update_by` VARCHAR(255) NOT NULL DEFAULT '50' COMMENT '更新人',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '修改时间',
    `del_flag` VARCHAR(255) NOT NULL DEFAULT '1' COMMENT '删除标记'
);
CREATE TABLE `quality_rule`(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `task_id` BIGINT NOT NULL COMMENT '数据集ID',
    `rule_name` VARCHAR(255) NOT NULL COMMENT '规则名称',
    `rule_type` TINYINT NOT NULL COMMENT '规则类型，1-单表校验 2-多表校验 3-自定义SQL',
    `where_content` VARCHAR(255) NOT NULL DEFAULT '500' COMMENT '过滤条件',
    `template_id` BIGINT NOT NULL COMMENT '规则模版ID',
    `column_` BIGINT NOT NULL
);
ALTER TABLE
    `data_set` ADD CONSTRAINT `data_set_project_id_foreign` FOREIGN KEY(`project_id`) REFERENCES `quality_project`(`id`);
ALTER TABLE
    `data_set` ADD CONSTRAINT `data_set_data_source_id_foreign` FOREIGN KEY(`data_source_id`) REFERENCES `data_source`(`id`);
