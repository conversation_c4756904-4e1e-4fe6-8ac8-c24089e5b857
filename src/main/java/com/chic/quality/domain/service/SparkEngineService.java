package com.chic.quality.domain.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.infrastructure.general.util.SparkUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname SparkEngineService
 * @description spark引擎服务
 * @date 2024/12/13 18:32
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class SparkEngineService {
    private static final String DS = "spark";
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @DS(DS)
    public boolean checkSql(DataSource ds, String sql){
        return SparkUtil.checkSql(jdbcTemplate,ds,sql);
    }

    @DS(DS)
    public boolean checkConnection(DataSource ds){
        return SparkUtil.checkConnection(jdbcTemplate,ds);
    }

    @DS(DS)
    public Object queryForObject(DataSource ds, String sql){
        return SparkUtil.queryForObject(jdbcTemplate,ds,sql);
    }
    @DS(DS)
    public LinkedHashMap<String, Integer> queryColumnType(DataSource ds, String sql){
        return SparkUtil.queryColumnTypeByDesc(jdbcTemplate,ds,sql);
    }

    @DS(DS)
    public String createTempView(DataSource ds, String sql){
        return SparkUtil.createTemporaryView(jdbcTemplate,ds,sql);
    }
    @DS(DS)
    public Object queryForObject(String sql){
        return SparkUtil.queryForObject(jdbcTemplate,sql);
    }

    @DS(DS)
    public List<Map<String, Object>> queryForList(DataSource ds, String sql){
        return SparkUtil.queryForList(jdbcTemplate,ds,sql);
    }
    @DS(DS)
    public List<Map<String, Object>> queryForList(String sql){
        return jdbcTemplate.queryForList(sql);
    }

    @DS(DS)
    public Map<String,String> createTempViewReturnMap(DataSource ds, String sql){
        return SparkUtil.createTempViewReturnMap(jdbcTemplate,ds,sql);
    }






}
